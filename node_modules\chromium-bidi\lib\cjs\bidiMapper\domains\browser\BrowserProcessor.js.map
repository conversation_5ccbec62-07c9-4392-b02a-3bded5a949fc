{"version": 3, "file": "BrowserProcessor.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/domains/browser/BrowserProcessor.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAEH,+DAKuC;AAGvC,MAAa,gBAAgB;IAClB,iBAAiB,CAAY;IAEtC,YAAY,gBAA2B;QACrC,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;IAC5C,CAAC;IAED,KAAK;QACH,sDAAsD;QACtD,6DAA6D;QAC7D,UAAU,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC;QAEzE,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,iBAAiB;QACrB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CACtD,6BAA6B,CAC9B,CAAC;QACF,OAAO;YACL,WAAW,EAAE,OAAO,CAAC,gBAAgB;SACtC,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,iBAAiB,CACrB,WAAgC;QAEhC,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;YAC9B,MAAM,IAAI,sCAAwB,CAChC,0CAA0C,CAC3C,CAAC;QACJ,CAAC;QACD,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,8BAA8B,EAAE;gBACvE,gBAAgB,EAAE,WAAW;aAC9B,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,mKAAmK;YACnK,IAAK,GAAa,CAAC,OAAO,CAAC,UAAU,CAAC,gCAAgC,CAAC,EAAE,CAAC;gBACxE,MAAM,IAAI,wCAA0B,CAAE,GAAa,CAAC,OAAO,CAAC,CAAC;YAC/D,CAAC;YACD,MAAM,GAAG,CAAC;QACZ,CAAC;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,eAAe;QACnB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CACrD,2BAA2B,CAC5B,CAAC;QACF,OAAO;YACL,YAAY,EAAE;gBACZ;oBACE,WAAW,EAAE,SAAS;iBACvB;gBACD,GAAG,MAAM,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE;oBACrC,OAAO;wBACL,WAAW,EAAE,EAAE;qBAChB,CAAC;gBACJ,CAAC,CAAC;aACH;SACF,CAAC;IACJ,CAAC;CACF;AA/DD,4CA+DC"}