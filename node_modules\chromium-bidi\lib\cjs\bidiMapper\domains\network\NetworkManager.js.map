{"version": 3, "file": "NetworkManager.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/domains/network/NetworkManager.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAaH,2DAAmD;AAGnD,6BAA6B;AAC7B,MAAa,cAAc;IAChB,UAAU,CAAY;IACtB,aAAa,CAAe;IAC5B,eAAe,CAAiB;IAEzC,YACE,SAAoB,EACpB,YAA0B,EAC1B,cAA8B;QAE9B,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;IACxC,CAAC;IAED,2EAA2E;IAC3E,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED;;;OAGG;IACH,0BAA0B,CACxB,EAAmB,EACnB,aAAsB;QAEtB,IAAI,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;QAClD,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,OAAO,GAAG,IAAI,kCAAc,CAC1B,EAAE,EACF,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,UAAU,EACf,aAAa,CACd,CAAC;QAEF,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAEzC,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,MAAM,CAAC,MAAM,CACX,SAAoB,EACpB,YAA0B,EAC1B,cAA8B;QAE9B,MAAM,cAAc,GAAG,IAAI,cAAc,CACvC,SAAS,EACT,YAAY,EACZ,cAAc,CACf,CAAC;QAEF,SAAS,CAAC,gBAAgB,CAAC,EAAE,CAC3B,2BAA2B,EAC3B,CAAC,MAA+C,EAAE,EAAE;YAClD,IAAI,SAAS,CAAC,SAAS,CAAC,SAAS,KAAK,MAAM,CAAC,SAAS,EAAE,CAAC;gBACvD,cAAc,CAAC,eAAe,CAAC,iBAAiB,EAAE,CAAC;YACrD,CAAC;QACH,CAAC,CACF,CAAC;QAEF,SAAS,CAAC,SAAS,CAAC,EAAE,CACpB,2BAA2B,EAC3B,CAAC,MAA+C,EAAE,EAAE;YAClD,MAAM,OAAO,GAAG,cAAc,CAAC,eAAe,CAAC,UAAU,CACvD,MAAM,CAAC,SAAS,CACjB,CAAC;YACF,IAAI,OAAO,IAAI,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;gBACvC,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;gBAC/B,cAAc,CAAC,eAAe,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBAC/D,cAAc;qBACX,0BAA0B,CACzB,MAAM,CAAC,SAAS,EAChB,OAAO,CAAC,aAAa,GAAG,CAAC,CAC1B;qBACA,wBAAwB,CAAC,MAAM,CAAC,CAAC;YACtC,CAAC;iBAAM,IAAI,OAAO,EAAE,CAAC;gBACnB,OAAO,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;YAC3C,CAAC;iBAAM,CAAC;gBACN,cAAc;qBACX,0BAA0B,CAAC,MAAM,CAAC,SAAS,CAAC;qBAC5C,wBAAwB,CAAC,MAAM,CAAC,CAAC;YACtC,CAAC;QACH,CAAC,CACF,CAAC;QAEF,SAAS,CAAC,SAAS,CAAC,EAAE,CACpB,oCAAoC,EACpC,CAAC,MAAwD,EAAE,EAAE;YAC3D,cAAc;iBACX,0BAA0B,CAAC,MAAM,CAAC,SAAS,CAAC;iBAC5C,iCAAiC,CAAC,MAAM,CAAC,CAAC;QAC/C,CAAC,CACF,CAAC;QAEF,SAAS,CAAC,SAAS,CAAC,EAAE,CACpB,0BAA0B,EAC1B,CAAC,MAA8C,EAAE,EAAE;YACjD,cAAc;iBACX,0BAA0B,CAAC,MAAM,CAAC,SAAS,CAAC;iBAC5C,uBAAuB,CAAC,MAAM,CAAC,CAAC;QACrC,CAAC,CACF,CAAC;QAEF,SAAS,CAAC,SAAS,CAAC,EAAE,CACpB,mCAAmC,EACnC,CAAC,MAAuD,EAAE,EAAE;YAC1D,cAAc;iBACX,0BAA0B,CAAC,MAAM,CAAC,SAAS,CAAC;iBAC5C,gCAAgC,CAAC,MAAM,CAAC,CAAC;QAC9C,CAAC,CACF,CAAC;QAEF,SAAS,CAAC,SAAS,CAAC,EAAE,CACpB,gCAAgC,EAChC,CAAC,MAAoD,EAAE,EAAE;YACvD,cAAc;iBACX,0BAA0B,CAAC,MAAM,CAAC,SAAS,CAAC;iBAC5C,iBAAiB,EAAE,CAAC;QACzB,CAAC,CACF,CAAC;QAEF,SAAS,CAAC,SAAS,CAAC,EAAE,CACpB,uBAAuB,EACvB,CAAC,MAA2C,EAAE,EAAE;YAC9C,cAAc;iBACX,0BAA0B,CAAC,MAAM,CAAC,SAAS,CAAC;iBAC5C,oBAAoB,CAAC,MAAM,CAAC,CAAC;QAClC,CAAC,CACF,CAAC;QAEF,oFAAoF;QACpF,SAAS,CAAC,SAAS,CAAC,EAAE,CACpB,qBAAqB,EACrB,CAAC,MAAyC,EAAE,EAAE;YAC5C,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;gBACrB,cAAc;qBACX,0BAA0B,CAAC,MAAM,CAAC,SAAS,CAAC;qBAC5C,eAAe,CAAC,MAAM,CAAC,CAAC;YAC7B,CAAC;QACH,CAAC,CACF,CAAC;QAEF,OAAO,cAAc,CAAC;IACxB,CAAC;CACF;AAtJD,wCAsJC"}