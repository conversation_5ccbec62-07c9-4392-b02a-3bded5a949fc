/**
 * Utility functions for the Underdog Fantasy automation service
 */

const fs = require('fs').promises;
const path = require('path');

/**
 * Create standardized API response
 * @param {string|null} shareLink - The generated share link or null
 * @param {string|null} error - Error message or null
 * @param {Object} additional - Additional data to include
 * @returns {Object} Standardized response object
 */
function createStandardResponse(shareLink, error, additional = {}) {
  return {
    share_link: shareLink,
    error: error,
    timestamp: new Date().toISOString(),
    success: !error,
    ...additional
  };
}

/**
 * Setup global error handling for Express app
 * @param {Object} app - Express application instance
 */
function setupErrorHandling(app) {
  // 404 handler
  app.use('*', (req, res) => {
    res.status(404).json({
      error: 'Endpoint not found',
      path: req.originalUrl,
      method: req.method,
      timestamp: new Date().toISOString()
    });
  });

  // Global error handler
  app.use((error, req, res, next) => {
    console.error(`💥 Unhandled error in ${req.method} ${req.path}:`, error);

    res.status(500).json({
      error: 'Internal server error',
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  });
}

/**
 * Generate unique filename with timestamp
 * @param {string} prefix - Filename prefix
 * @param {string} extension - File extension (without dot)
 * @returns {string} Unique filename
 */
function generateUniqueFilename(prefix, extension) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const random = Math.random().toString(36).substring(2, 8);
  return `${prefix}_${timestamp}_${random}.${extension}`;
}

/**
 * Save debug artifact (screenshot, HTML, etc.)
 * @param {string} type - Type of artifact (screenshot, html, info)
 * @param {string} content - Content to save
 * @param {string} context - Context description
 * @returns {Promise<string>} Path to saved file
 */
async function saveDebugArtifact(type, content, context = '') {
  try {
    const debugDir = './debug';

    // Ensure directory exists
    try {
      await fs.mkdir(debugDir, { recursive: true });
    } catch (e) {
      // Directory might already exist
    }

    const extension = type === 'screenshot' ? 'png' : (type === 'html' ? 'html' : 'json');
    const prefix = context ? `${context}_${type}` : type;
    const filename = generateUniqueFilename(prefix, extension);
    const filepath = path.join(debugDir, filename);

    if (type === 'screenshot') {
      // Content is buffer for screenshots
      await fs.writeFile(filepath, content);
    } else {
      // Content is string for HTML/JSON
      await fs.writeFile(filepath, content, 'utf8');
    }

    console.log(`📁 Debug artifact saved: ${filepath}`);
    return filepath;
  } catch (error) {
    console.error('❌ Error saving debug artifact:', error);
    return null;
  }
}

/**
 * Clean up old debug artifacts
 * @param {number} retentionHours - Hours to retain files
 */
async function cleanupDebugArtifacts(retentionHours = 24) {
  try {
    const debugDir = './debug';

    // Check if directory exists
    try {
      await fs.access(debugDir);
    } catch (e) {
      return; // Directory doesn't exist
    }

    const files = await fs.readdir(debugDir);
    const cutoffTime = Date.now() - (retentionHours * 60 * 60 * 1000);

    let deletedCount = 0;

    for (const file of files) {
      const filepath = path.join(debugDir, file);
      try {
        const stats = await fs.stat(filepath);

        if (stats.mtime.getTime() < cutoffTime) {
          await fs.unlink(filepath);
          deletedCount++;
        }
      } catch (e) {
        // Ignore errors for individual files
      }
    }

    if (deletedCount > 0) {
      console.log(`🧹 Cleaned up ${deletedCount} old debug artifacts`);
    }
  } catch (error) {
    console.error('❌ Error cleaning up debug artifacts:', error);
  }
}

/**
 * Wait for a specified amount of time
 * @param {number} ms - Milliseconds to wait
 * @returns {Promise} Promise that resolves after the specified time
 */
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Retry a function with exponential backoff
 * @param {Function} fn - Function to retry
 * @param {number} maxAttempts - Maximum number of attempts
 * @param {number} baseDelay - Base delay in milliseconds
 * @param {string} context - Context for logging
 * @returns {Promise} Result of the function
 */
async function retryWithBackoff(fn, maxAttempts = 3, baseDelay = 1000, context = 'operation') {
  let lastError;

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      console.log(`🔄 Attempting ${context} (${attempt}/${maxAttempts})`);
      const result = await fn();
      console.log(`✅ ${context} succeeded on attempt ${attempt}`);
      return result;
    } catch (error) {
      lastError = error;
      console.log(`❌ ${context} failed on attempt ${attempt}: ${error.message}`);

      if (attempt < maxAttempts) {
        const delay = baseDelay * Math.pow(2, attempt - 1);
        console.log(`⏳ Waiting ${delay}ms before retry...`);
        await sleep(delay);
      }
    }
  }

  throw new Error(`${context} failed after ${maxAttempts} attempts. Last error: ${lastError.message}`);
}

/**
 * Validate bet ID format
 * @param {string} betId - Bet ID to validate
 * @returns {boolean} True if valid UUID format
 */
function isValidBetId(betId) {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return typeof betId === 'string' && uuidRegex.test(betId);
}

/**
 * Sanitize string for logging (remove sensitive data)
 * @param {string} str - String to sanitize
 * @returns {string} Sanitized string
 */
function sanitizeForLogging(str) {
  if (!str) return str;

  // Replace potential passwords, tokens, etc.
  return str
    .replace(/password[=:]\s*[^\s&]+/gi, 'password=***')
    .replace(/token[=:]\s*[^\s&]+/gi, 'token=***')
    .replace(/authorization[=:]\s*[^\s&]+/gi, 'authorization=***');
}

/**
 * Format duration in human readable format
 * @param {number} ms - Duration in milliseconds
 * @returns {string} Formatted duration
 */
function formatDuration(ms) {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);

  if (hours > 0) {
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
}

/**
 * Get memory usage information
 * @returns {Object} Memory usage stats
 */
function getMemoryUsage() {
  const usage = process.memoryUsage();
  return {
    rss: Math.round(usage.rss / 1024 / 1024) + ' MB',
    heapTotal: Math.round(usage.heapTotal / 1024 / 1024) + ' MB',
    heapUsed: Math.round(usage.heapUsed / 1024 / 1024) + ' MB',
    external: Math.round(usage.external / 1024 / 1024) + ' MB'
  };
}

module.exports = {
  createStandardResponse,
  setupErrorHandling,
  generateUniqueFilename,
  saveDebugArtifact,
  cleanupDebugArtifacts,
  sleep,
  retryWithBackoff,
  isValidBetId,
  sanitizeForLogging,
  formatDuration,
  getMemoryUsage
};
