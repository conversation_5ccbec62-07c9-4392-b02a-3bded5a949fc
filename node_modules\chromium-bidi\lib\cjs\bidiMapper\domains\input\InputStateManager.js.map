{"version": 3, "file": "InputStateManager.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/domains/input/InputStateManager.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAEH,wDAAgD;AAGhD,mDAA2C;AAE3C,4CAA4C;AAC5C,wEAAwE;AACxE,MAAa,iBAAkB,SAAQ,OAGtC;IACU,GAAG,CAAC,OAA4B;QACvC,IAAA,kBAAM,EAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC,CAAC;QAEpC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;YACvB,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,0BAAU,EAAE,CAAC,CAAC;QACtC,CAAC;QAED,OAAO,KAAK,CAAC,GAAG,CAAC,OAAO,CAAE,CAAC;IAC7B,CAAC;CACF;AAbD,8CAaC"}