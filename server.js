/**
 * Simplified HTTP server for Underdog Fantasy Automation
 */
const express = require('express');
const { config, validateConfig } = require('./config');
const underdogService = require('./underdogService');

// Initialize Express app
const app = express();

// Global variables for service state tracking
let serviceInitializationPromise = null; // Stores the promise of the ONE initialization attempt
let isServiceReady = false; // Flag to track if the service is ready to handle requests
let initializationError = null; // Stores any initialization error for reporting

console.log('Starting server initialization...');

try {
  // Validate configuration
  console.log('Validating configuration...');
  if (!validateConfig()) {
    console.error('CRITICAL FAILURE: Configuration validation failed');
    process.exit(1);
  }
  console.log('Configuration validated successfully');

  // Configure Express app
  console.log('Configuring Express app...');
  app.use(express.json());

  // Add request ID middleware
  app.use((req, res, next) => {
    req.requestId = `req-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;
    next();
  });

  // Add basic request logging middleware
  app.use((req, res, next) => {
    console.log(`[${req.requestId}] ${req.method} ${req.path} request received`);

    // Log response completion
    res.on('finish', () => {
      console.log(`[${req.requestId}] Response sent with status ${res.statusCode}`);
    });

    next();
  });

  // Add a simple health check endpoint
  app.get('/health', (req, res) => {
    const healthStatus = {
      status: isServiceReady ? 'ok' : 'initializing',
      initialized: underdogService.isInitialized,
      uptime: process.uptime(),
      timestamp: new Date().toISOString()
    };

    // Add initialization error if there is one
    if (initializationError) {
      healthStatus.error = initializationError.message;
      healthStatus.status = 'error';
    }

    res.json(healthStatus);
  });

  // Add the generate-links endpoint
  app.post('/generate-links', async (req, res) => {
    const requestId = req.requestId;
    console.log(`[${requestId}] Processing generate-links request`);

    try {
      // Check if service initialization has been attempted
      if (!serviceInitializationPromise) {
        console.error(`[${requestId}] Critical error: serviceInitializationPromise is null!`);
        return res.status(500).json({
          share_link: null,
          error: 'Service bootstrapping error: initialization was never attempted'
        });
      }

      // Wait for the initialization to complete if it's still in progress
      if (!isServiceReady && !initializationError) {
        console.log(`[${requestId}] Service initialization still in progress, waiting...`);

        try {
          await serviceInitializationPromise;
          console.log(`[${requestId}] Service initialization completed while waiting`);
        } catch (waitError) {
          console.error(`[${requestId}] Service initialization failed while waiting:`, waitError);
          return res.status(503).json({
            share_link: null,
            error: 'Service Unavailable: Underdog service failed to initialize'
          });
        }
      }

      // Check if the service is ready after waiting
      if (!isServiceReady) {
        console.error(`[${requestId}] Service is not ready after waiting for initialization`);
        const errorMessage = initializationError
          ? `Service Unavailable: ${initializationError.message}`
          : 'Service Unavailable: Underdog service failed to initialize';

        return res.status(503).json({
          share_link: null,
          error: errorMessage
        });
      }

      // Extract betIds from request body
      const { betIds } = req.body;

      // Validate betIds exists
      if (betIds === undefined) {
        console.log(`[${requestId}] Missing betIds in request body`);
        return res.status(400).json({
          share_link: null,
          error: 'Missing betIds in request body'
        });
      }

      // Validate betIds is an array
      if (!Array.isArray(betIds)) {
        console.log(`[${requestId}] betIds must be an array, got ${typeof betIds}`);
        return res.status(400).json({
          share_link: null,
          error: 'betIds must be an array'
        });
      }

      // Generate share link
      console.log(`[${requestId}] Calling Underdog service to generate share link`);
      const result = await underdogService.generateShareLinkForBet(betIds);

      // Return result
      const statusCode = result.error ? (result.error.includes('Invalid input') ? 422 : 500) : 200;
      return res.status(statusCode).json(result);
    } catch (error) {
      console.error(`[${requestId}] Unhandled error in generate-links endpoint:`, error);
      return res.status(500).json({
        share_link: null,
        error: 'An unexpected internal server error occurred'
      });
    }
  });

  // Error handling middleware
  app.use((err, req, res, next) => {
    console.error(`[${req.requestId || 'unknown'}] Unhandled error:`, err);
    res.status(500).json({
      share_link: null,
      error: 'Internal Server Error'
    });
  });

  console.log('Express app configured successfully');
} catch (error) {
  console.error('CRITICAL FAILURE: Error during server initialization:', error);
  process.exit(1);
}

// Start the server and initialize the Underdog service
async function startServerAndInitializeService() {
  try {
    console.log('Starting server and initializing service...');

    // Start the HTTP server first
    const port = config.server.port;
    const server = app.listen(port, () => {
      console.log(`Server running on port ${port}`);
      console.log('\n==================================================');
      console.log('HTTP SERVER IS READY! Underdog service initialization might still be in progress.');
      console.log('==================================================\n');
    });

    // Handle graceful shutdown
    process.on('SIGTERM', () => shutdown(server));
    process.on('SIGINT', () => shutdown(server));

    // Initialize the Underdog service (ONE TIME ONLY)
    console.log('Attempting ONE-TIME Underdog Service initialization...');

    // Store the initialization promise for later use
    serviceInitializationPromise = underdogService.initialize()
      .then(() => {
        isServiceReady = true;
        initializationError = null;
        console.log('CRITICAL SUCCESS: Underdog Service initialized and logged in successfully');
        console.log('\n==================================================');
        console.log('UNDERDOG SERVICE IS READY! The application is fully operational.');
        console.log('==================================================\n');
      })
      .catch(err => {
        isServiceReady = false;
        initializationError = err;
        console.error('CRITICAL FAILURE: Underdog Service failed to initialize:', err.message);
        console.log('\n==================================================');
        console.log('UNDERDOG SERVICE FAILED TO INITIALIZE! The application will return errors for API requests.');
        console.log('==================================================\n');
        throw err; // Re-throw to signal initialization failure
      });

    // Wait for initialization to complete, but don't block server startup
    try {
      await serviceInitializationPromise;
    } catch (error) {
      // We already logged the error and set the flags, so just continue
      console.log('Continuing server operation despite initialization failure');
    }

    return server;
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Graceful shutdown
async function shutdown(server) {
  console.log('\n==================================================');
  console.log('GRACEFUL SHUTDOWN INITIATED');
  console.log('==================================================\n');

  console.log('Shutting down server...');

  try {
    // Always attempt to shutdown the Underdog service first to ensure clean browser closure
    console.log('Shutting down Underdog service...');
    try {
      if (underdogService) {
        await underdogService.shutdown();
        console.log('Underdog service shut down successfully');
      } else {
        console.log('Underdog service was not available, skipping shutdown');
      }
    } catch (serviceError) {
      console.error('Error shutting down Underdog service:', serviceError);
      // Continue with shutdown even if service shutdown fails
    }

    // Close the HTTP server
    if (server) {
      await new Promise((resolve) => {
        server.close(() => {
          console.log('HTTP server closed');
          resolve();
        });
      });
    }

    console.log('\n==================================================');
    console.log('SERVER SHUTDOWN COMPLETE');
    console.log('==================================================\n');

    // Give a short delay to allow logs to be flushed
    setTimeout(() => {
      process.exit(0);
    }, 500);
  } catch (error) {
    console.error('Error during shutdown:', error);
    process.exit(1);
  }
}

// Export the app for testing
module.exports = app;

// Only start the server if this file is run directly (not required/imported)
if (require.main === module) {
  // Start the server and initialize the service
  console.log('Starting server and initializing service...');
  startServerAndInitializeService().catch(error => {
    console.error('Error starting server or initializing service:', error);
    process.exit(1);
  });
}
