{"version": 3, "file": "NetworkProcessor.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/domains/network/NetworkProcessor.ts"], "names": [], "mappings": ";;;AAkBA,+DAKuC;AACvC,wDAAgD;AAIhD,2DAAwE;AACxE,uDAG2B;AAE3B,0CAA0C;AAC1C,MAAa,gBAAgB;IAClB,uBAAuB,CAAyB;IAChD,eAAe,CAAiB;IAEzC,YACE,sBAA8C,EAC9C,cAA8B;QAE9B,IAAI,CAAC,uBAAuB,GAAG,sBAAsB,CAAC;QACtD,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;IACxC,CAAC;IAED,KAAK,CAAC,YAAY,CAChB,MAAsC;QAEtC,0EAA0E;QAC1E,uBAAuB;QACvB,IACE,MAAM,CAAC,MAAM,CAAC,QAAQ,0DAAqC;YAC3D,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,oEAA0C,EACjE,CAAC;YACD,MAAM,CAAC,MAAM,CAAC,OAAO,oEAA0C,CAAC;QAClE,CAAC;QAED,MAAM,WAAW,GAAyB,MAAM,CAAC,WAAW,IAAI,EAAE,CAAC;QACnE,MAAM,iBAAiB,GACrB,gBAAgB,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;QAEjD,MAAM,SAAS,GAAsB,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC;YACrE,WAAW,EAAE,iBAAiB;YAC9B,MAAM,EAAE,MAAM,CAAC,MAAM;SACtB,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;QAEzB,OAAO;YACL,SAAS;SACV,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,eAAe,CACnB,MAAyC;QAEzC,MAAM,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC;QACjC,MAAM,EAAC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAErE,IAAI,KAAK,uEAA6C,EAAE,CAAC;YACvD,MAAM,IAAI,sCAAwB,CAChC,mCAAmC,SAAS,uCAAuC,CACpF,CAAC;QACJ,CAAC;QAED,IAAI,MAAM,CAAC,GAAG,KAAK,SAAS,EAAE,CAAC;YAC7B,gBAAgB,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC9C,CAAC;QAED,MAAM,EAAC,GAAG,EAAE,MAAM,EAAE,OAAO,EAAC,GAAG,MAAM,CAAC;QACtC,sBAAsB;QACtB,oBAAoB;QACpB,kBAAkB;QAElB,MAAM,cAAc,GAClB,IAAA,uDAAqC,EAAC,OAAO,CAAC,CAAC;QAEjD,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QAClD,MAAM,OAAO,CAAC,eAAe,CAAC,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,cAAc,CAAC,CAAC;QAEpE,IAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;QAErD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,gBAAgB,CACpB,MAA0C;QAE1C,MAAM,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC;QACjC,MAAM,EAAC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAErE,IAAI,KAAK,uEAA6C,EAAE,CAAC;YACvD,MAAM,IAAI,sCAAwB,CAChC,mCAAmC,SAAS,mCAAmC,CAChF,CAAC;QACJ,CAAC;QAED,MAAM,EAAC,UAAU,EAAE,YAAY,EAAE,OAAO,EAAC,GAAG,MAAM,CAAC;QAEnD,MAAM,eAAe,GACnB,IAAA,uDAAqC,EAAC,OAAO,CAAC,CAAC;QAEjD,sBAAsB;QACtB,qBAAqB;QACrB,yBAAyB;QACzB,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QAClD,MAAM,OAAO,CAAC,gBAAgB,CAC5B,OAAO,EACP,UAAU,EACV,YAAY,EACZ,eAAe,CAChB,CAAC;QAEF,IAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;QAErD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,gBAAgB,CACpB,MAA0C;QAE1C,MAAM,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC;QACjC,MAAM,EAAC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAErE,IAAI,KAAK,6DAAwC,EAAE,CAAC;YAClD,MAAM,IAAI,sCAAwB,CAChC,mCAAmC,SAAS,kCAAkC,CAC/E,CAAC;QACJ,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QAElD,IAAI,QAA4B,CAAC;QACjC,IAAI,QAA4B,CAAC;QAEjC,IAAI,MAAM,CAAC,MAAM,KAAK,oBAAoB,EAAE,CAAC;YAC3C,MAAM,EAAC,WAAW,EAAC,GAAG,MAAM,CAAC;YAE7B,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;YAChC,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;YAChC,mDAAmD;YACnD,+BAA+B;YAC/B,IAAA,kBAAM,EACJ,WAAW,CAAC,IAAI,KAAK,UAAU,EAC/B,oBAAoB,WAAW,CAAC,IAAI,qBAAqB,CAC1D,CAAC;QACJ,CAAC;QAED,MAAM,QAAQ,GAAG,IAAA,4EAA0D,EACzE,MAAM,CAAC,MAAM,CACd,CAAC;QAEF,MAAM,OAAO,CAAC,gBAAgB,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAEtE,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,EAChB,OAAO,EAAE,SAAS,GACY;QAC9B,MAAM,EAAC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAErE,IAAI,KAAK,6DAAwC,EAAE,CAAC;YAClD,MAAM,IAAI,sCAAwB,CAChC,mCAAmC,SAAS,8BAA8B,CAC3E,CAAC;QACJ,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QAClD,MAAM,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QAE7C,IAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;QAErD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,eAAe,CACnB,MAAyC;QAEzC,MAAM,EACJ,UAAU,EACV,YAAY,EACZ,OAAO,EACP,IAAI,EACJ,OAAO,EAAE,SAAS,GACnB,GAAG,MAAM,CAAC;QACX,MAAM,EAAC,OAAO,EAAE,OAAO,EAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAE9D,eAAe;QACf,yEAAyE;QAEzE,MAAM,eAAe,GACnB,IAAA,uDAAqC,EAAC,OAAO,CAAC,CAAC;QAEjD,sBAAsB;QACtB,qBAAqB;QACrB,yBAAyB;QACzB,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QAClD,MAAM,OAAO,CAAC,eAAe,CAC3B,OAAO,EACP,UAAU,IAAI,OAAO,CAAC,UAAU,EAChC,YAAY,EACZ,eAAe,EACf,IAAI,EAAE,KAAK,CAAC,+BAA+B;SAC5C,CAAC;QAEF,IAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;QAErD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,eAAe,CACnB,MAAyC;QAEzC,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAEvD,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;QAEzB,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,+EAA+E;IAC/E,KAAK,CAAC,YAAY;QAChB,MAAM,OAAO,CAAC,GAAG,CACf,IAAI,CAAC,uBAAuB,CAAC,cAAc,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YAClE,MAAM,OAAO,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;QACxC,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAED,iFAAiF;IACjF,KAAK,CAAC,aAAa;QACjB,MAAM,OAAO,CAAC,GAAG,CACf,IAAI,CAAC,uBAAuB,CAAC,cAAc,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YAClE,MAAM,OAAO,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;QACzC,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK,CAAC,WAAW;QACf,IACE,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE;YACpC,IAAI,CAAC,eAAe,CAAC,kBAAkB,EAAE;YACzC,IAAI,CAAC,eAAe,CAAC,kBAAkB,EAAE,EACzC,CAAC;YACD,wEAAwE;YACxE,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;QAC5B,CAAC;aAAM,CAAC;YACN,gEAAgE;YAChE,oBAAoB;YACpB,4BAA4B;YAC5B,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;QAC7B,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,kBAAkB,CAAC,SAA0B;QAC3C,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QACzE,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,MAAM,IAAI,oCAAsB,CAC9B,4CAA4C,SAAS,GAAG,CACzD,CAAC;QACJ,CAAC;QACD,OAAO,cAAc,CAAC;IACxB,CAAC;IAED,iBAAiB,CAAC,EAAmB;QACnC,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;QACpD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,oCAAsB,CAC9B,2BAA2B,EAAE,gBAAgB,CAC9C,CAAC;QACJ,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,cAAc,CAAC,GAAW;QAC/B,IAAI,CAAC;YACH,OAAO,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sCAAwB,CAAC,gBAAgB,GAAG,MAAM,KAAK,EAAE,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAED,MAAM,CAAC,gBAAgB,CACrB,WAAiC;QAEjC,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;YACpC,QAAQ,UAAU,CAAC,IAAI,EAAE,CAAC;gBACxB,KAAK,QAAQ,CAAC,CAAC,CAAC;oBACd,gBAAgB,CAAC,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;oBACpD,OAAO,UAAU,CAAC;gBACpB,CAAC;gBACD,KAAK,SAAS;oBACZ,oCAAoC;oBACpC,IACE,UAAU,CAAC,QAAQ,KAAK,SAAS;wBACjC,UAAU,CAAC,QAAQ,KAAK,SAAS;wBACjC,UAAU,CAAC,IAAI,KAAK,SAAS;wBAC7B,UAAU,CAAC,QAAQ,KAAK,SAAS;wBACjC,UAAU,CAAC,MAAM,KAAK,SAAS,EAC/B,CAAC;wBACD,OAAO,UAAU,CAAC;oBACpB,CAAC;oBAED,IAAI,UAAU,CAAC,QAAQ,KAAK,EAAE,EAAE,CAAC;wBAC/B,MAAM,IAAI,sCAAwB,CAChC,qCAAqC,CACtC,CAAC;oBACJ,CAAC;oBAED,IAAI,UAAU,CAAC,QAAQ,KAAK,EAAE,EAAE,CAAC;wBAC/B,MAAM,IAAI,sCAAwB,CAChC,qCAAqC,CACtC,CAAC;oBACJ,CAAC;oBAED,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;wBAC3C,IAAI,UAAU,CAAC,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;4BACzC,MAAM,IAAI,sCAAwB,CAChC,uCAAuC,CACxC,CAAC;wBACJ,CAAC;wBAED,IAAI,UAAU,CAAC,QAAQ,EAAE,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;4BACvC,MAAM,IAAI,sCAAwB,CAChC,+CAA+C,CAChD,CAAC;wBACJ,CAAC;oBACH,CAAC;oBAED,IAAI,UAAU,CAAC,IAAI,KAAK,EAAE,EAAE,CAAC;wBAC3B,MAAM,IAAI,sCAAwB,CAChC,iCAAiC,CAClC,CAAC;oBACJ,CAAC;oBAED,IAAI,CAAC;wBACH,IAAI,GAAG,CAAC,kCAAc,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC,CAAC;oBAC5D,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,MAAM,IAAI,sCAAwB,CAAC,GAAG,KAAK,EAAE,CAAC,CAAC;oBACjD,CAAC;oBACD,OAAO,UAAU,CAAC;YACtB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AA7VD,4CA6VC"}