console.log('🚀 Minimal test starting...');
console.log('📊 Node.js version:', process.version);
console.log('🖥️ Platform:', process.platform);
console.log('📁 Working directory:', process.cwd());

const express = require('express');
console.log('✅ Express loaded successfully');

const app = express();
const port = 3000;

app.use(express.json());

app.get('/test', (req, res) => {
  console.log('📥 Test endpoint hit');
  res.json({
    message: 'Minimal test server is working!',
    timestamp: new Date().toISOString(),
    nodeVersion: process.version
  });
});

app.post('/bet-test', (req, res) => {
  console.log('🎯 Bet test endpoint hit');
  console.log('📝 Body:', req.body);
  
  const { betIds } = req.body;
  
  res.json({
    message: 'Bet test endpoint working!',
    receivedBetIds: betIds,
    count: betIds ? betIds.length : 0,
    timestamp: new Date().toISOString()
  });
});

console.log('🌐 Starting minimal server...');
const server = app.listen(port, () => {
  console.log(`✅ Minimal server running on port ${port}`);
  console.log(`🔗 Test URL: http://localhost:${port}/test`);
  console.log(`🎯 Bet test URL: http://localhost:${port}/bet-test`);
});

console.log('🎉 Minimal test setup complete');

// Test the server internally
setTimeout(() => {
  console.log('🔄 Internal test starting...');
  
  const http = require('http');
  const options = {
    hostname: 'localhost',
    port: port,
    path: '/test',
    method: 'GET'
  };

  const req = http.request(options, (res) => {
    console.log(`✅ Internal test response: ${res.statusCode}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      console.log('📊 Internal test result:', data);
    });
  });

  req.on('error', (e) => {
    console.error('❌ Internal test error:', e.message);
  });

  req.end();
}, 2000);
