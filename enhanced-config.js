require('dotenv').config();

const config = {
  // Server configuration
  server: {
    port: process.env.PORT || 3000,
    host: process.env.HOST || 'localhost'
  },

  // Underdog Fantasy credentials
  credentials: {
    email: process.env.UNDERDOG_USERNAME,
    password: process.env.UNDERDOG_PASSWORD
  },

  // Proxy configuration
  proxy: {
    enabled: !!process.env.PROXY_SERVER,
    server: process.env.PROXY_SERVER,
    username: process.env.PROXY_USERNAME,
    password: process.env.PROXY_PASSWORD,
    type: process.env.PROXY_TYPE || 'http',
    bypass: process.env.PROXY_BYPASS || '<-loopback>'
  },

  // Browser configuration
  browser: {
    headless: process.env.HEADLESS === 'true',
    userDataDir: process.env.USER_DATA_DIR || './user_data',
    viewport: {
      width: parseInt(process.env.VIEWPORT_WIDTH) || 1366,
      height: parseInt(process.env.VIEWPORT_HEIGHT) || 768
    }
  },

  // Timeouts (in milliseconds)
  timeouts: {
    login: parseInt(process.env.LOGIN_TIMEOUT_MS) || 60000,
    navigation: parseInt(process.env.NAVIGATION_TIMEOUT_MS) || 45000,
    elementWait: parseInt(process.env.ELEMENT_WAIT_MS) || 15000,
    locationVerification: parseInt(process.env.LOCATION_VERIFICATION_MS) || 30000
  },

  // Retry configuration
  retry: {
    maxLoginAttempts: parseInt(process.env.MAX_LOGIN_ATTEMPTS) || 3,
    loginRetryDelay: parseInt(process.env.LOGIN_RETRY_DELAY_MS) || 5000,
    maxLocationAttempts: parseInt(process.env.MAX_LOCATION_ATTEMPTS) || 2,
    locationRetryDelay: parseInt(process.env.LOCATION_RETRY_DELAY_MS) || 3000
  },

  // Session management
  session: {
    refreshInterval: parseInt(process.env.SESSION_REFRESH_INTERVAL_MINUTES) * 60 * 1000 || 5 * 60 * 1000,
    locationRefreshInterval: parseInt(process.env.LOCATION_REFRESH_INTERVAL_MINUTES) * 60 * 1000 || 4 * 60 * 1000
  },

  // Stealth configuration
  stealth: {
    enabled: process.env.STEALTH_ENABLED !== 'false',
    removeWebdriver: process.env.REMOVE_WEBDRIVER !== 'false',
    fakeUserAgent: process.env.FAKE_USER_AGENT !== 'false',
    fakeViewport: process.env.FAKE_VIEWPORT !== 'false',
    fakeLanguages: process.env.FAKE_LANGUAGES !== 'false',
    fakeTimezone: process.env.FAKE_TIMEZONE === 'true'
  },

  // Underdog Fantasy specific selectors and endpoints
  selectors: {
    login: {
      emailInput: 'input[placeholder="Email"]',
      passwordInput: 'input[type="password"]',
      submitButton: 'button[type="submit"], button.styles__button__E1IZ_',
      errorMessage: '.styles__errorMessage__3Aq0_, .error, .alert-error',
      successIndicator: '.styles__overUnderCell__by1xI, [data-testid*="pick"], .pick-em'
    },
    location: {
      contentIndicators: [
        '.styles__overUnderCell__by1xI',
        '[data-testid*="pick"]',
        '.pick-em',
        '.bet-card'
      ],
      errorIndicators: [
        '[class*="location"][class*="error"]',
        '[data-testid*="location"][data-testid*="error"]',
        '.geo-error',
        '.location-blocked'
      ],
      loadingIndicators: [
        '.loading',
        '.spinner',
        '[data-testid*="loading"]',
        '[class*="loading"]'
      ]
    }
  },

  endpoints: {
    underdogLogin: 'https://underdogfantasy.com/pick-em/higher-lower/all/home',
    underdogPickem: 'https://underdogfantasy.com/pick-em/higher-lower/all/home',
    underdogLobby: 'https://underdogfantasy.com/lobby',
    underdogProfile: 'https://underdogfantasy.com/profile'
  },

  // Debug configuration
  debug: {
    enabled: process.env.DEBUG_ENABLED === 'true',
    screenshotsEnabled: process.env.DEBUG_SCREENSHOTS !== 'false',
    htmlDumpsEnabled: process.env.DEBUG_HTML_DUMPS !== 'false',
    networkLogging: process.env.DEBUG_NETWORK_LOGGING !== 'false',
    artifactsDir: process.env.DEBUG_ARTIFACTS_DIR || './debug_artifacts'
  },

  // User agent strings for different scenarios
  userAgents: {
    chrome: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36',
    firefox: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:123.0) Gecko/20100101 Firefox/123.0',
    edge: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 Edg/122.0.0.0'
  },

  // Geolocation settings (US-based for Underdog Fantasy compliance)
  geolocation: {
    latitude: 40.7128,  // New York City
    longitude: -74.0060,
    accuracy: 100
  },

  // Timezone setting
  timezone: 'America/New_York',

  // Languages
  languages: ['en-US', 'en'],

  // Puppeteer launch arguments for enhanced stealth
  launchArgs: [
    '--no-sandbox',
    '--disable-setuid-sandbox',
    '--disable-dev-shm-usage',
    '--disable-accelerated-2d-canvas',
    '--no-first-run',
    '--no-zygote',
    '--disable-gpu',
    '--disable-blink-features=AutomationControlled',
    '--disable-features=VizDisplayCompositor',
    '--disable-web-security',
    '--disable-features=TranslateUI',
    '--disable-ipc-flooding-protection',
    '--disable-renderer-backgrounding',
    '--disable-backgrounding-occluded-windows',
    '--disable-client-side-phishing-detection',
    '--disable-component-extensions-with-background-pages',
    '--disable-default-apps',
    '--disable-extensions',
    '--disable-hang-monitor',
    '--disable-prompt-on-repost',
    '--disable-sync',
    '--metrics-recording-only',
    '--no-default-browser-check',
    '--no-pings',
    '--password-store=basic',
    '--use-mock-keychain',
    '--force-color-profile=srgb'
  ]
};

// Validation function
function validateConfig() {
  const errors = [];

  // Check required credentials
  if (!config.credentials.email) {
    errors.push('UNDERDOG_USERNAME environment variable is required');
  }
  if (!config.credentials.password) {
    errors.push('UNDERDOG_PASSWORD environment variable is required');
  }

  // Check proxy configuration if enabled
  if (config.proxy.enabled) {
    if (!config.proxy.server) {
      errors.push('PROXY_SERVER is required when proxy is enabled');
    }
    if (!config.proxy.username || !config.proxy.password) {
      console.warn('⚠️ Proxy authentication not configured');
    }
  }

  // Check timeout values
  if (config.timeouts.login < 10000) {
    console.warn('⚠️ Login timeout is very low, consider increasing');
  }

  if (errors.length > 0) {
    throw new Error(`Configuration validation failed:\n${errors.join('\n')}`);
  }

  return true;
}

// Export configuration with validation
module.exports = {
  ...config,
  validate: validateConfig
};
