# Deployment Guide - Underdog Fantasy Automation v1.4

## 🎯 User-Location-Token Enhancement Deployment

This guide covers deploying the enhanced version that specifically addresses User-Location-Token capture issues.

## 📋 Prerequisites

### System Requirements
- **Node.js**: v16+ (tested with v20.11.1)
- **Operating System**: Windows/Linux/macOS
- **Memory**: 2GB+ RAM (for browser automation)
- **Network**: Stable internet connection

### Required Services
- **Proxy Service**: US-based proxy (required for geo-restrictions)
- **Underdog Fantasy Account**: Valid credentials

## 🚀 Step-by-Step Deployment

### 1. Environment Setup

```bash
# Navigate to the application directory
cd underdog-simplified-update-1.4

# Install dependencies
npm install
```

### 2. Configuration

Create/edit `.env` file:

```env
# Underdog Fantasy Credentials
UNDERDOG_USERNAME=<EMAIL>
UNDERDOG_PASSWORD=M36984250u

# Proxy Configuration (CRITICAL for geo-restrictions)
PROXY_SERVER=*************:12323
PROXY_USERNAME=14afa5bcaad0e
PROXY_PASSWORD=8218998f56
PROXY_TYPE=http

# Server Configuration
PORT=3000
HEADLESS=false

# Browser Configuration
USER_DATA_DIR=./user_data

# Enhanced Timeouts for Token Capture
LOGIN_TIMEOUT_MS=60000
NAVIGATION_TIMEOUT_MS=45000
SESSION_REFRESH_INTERVAL_MINUTES=120
LOCATION_REFRESH_INTERVAL_MINUTES=4

# Stealth Configuration
STEALTH_ENABLED=true
REMOVE_WEBDRIVER=true
FAKE_USER_AGENT=true
FAKE_VIEWPORT=true
FAKE_LANGUAGES=true

# Enhanced Retry Configuration
MAX_LOGIN_ATTEMPTS=3
LOGIN_RETRY_DELAY_MS=5000
MAX_LOCATION_REFRESH_ATTEMPTS=2
```

### 3. Proxy Verification

Test proxy connectivity before starting:

```bash
# Test proxy connection (Windows)
curl --proxy http://14afa5bcaad0e:8218998f56@*************:12323 https://ipinfo.io

# Expected response should show US location
{
  "ip": "*************",
  "city": "New York City",
  "region": "New York",
  "country": "US",
  "loc": "40.7143,-74.0060"
}
```

### 4. Start Application

```bash
# Start the server
node server.js
```

Expected startup sequence:
```
Stealth plugin enabled with configured evasions
Starting server initialization...
Configuration validated successfully
Server running on port 3000
Proxy test result: {"ip": "*************", "country": "US"}
Browser launched successfully
Login successful on attempt 1
Auth tokens captured successfully
CRITICAL SUCCESS: Underdog service initialized and logged in successfully
```

### 5. Health Check

```bash
# Verify service is ready
curl http://localhost:3000/health
```

Expected response:
```json
{
  "status": "ready",
  "initialized": true,
  "uptime": 45.123,
  "timestamp": "2025-05-24T16:00:00.000Z"
}
```

### 6. Test API with Provided IDs

```bash
# Test bet link generation
curl -X POST http://localhost:3000/generate-links \
  -H "Content-Type: application/json" \
  -d '{
    "betIds": [
      "55875246-0e29-4387-a20e-9a0ef9a968ba",
      "e0847a4a-4aa6-4b3a-b42f-6bbe7a81f86f",
      "a763577d-d951-45bf-897d-e8264f0db863"
    ]
  }'
```

## 🔍 Enhanced Debugging Features

### Token Capture Monitoring

The v1.4 system provides detailed logging for User-Location-Token capture:

```
[TokenCapture] Checking JavaScript variables for location tokens...
[TokenCapture] Location token found in window.userLocationToken: eyJhbGciOiJSUzI1NiIs...
[TokenCapture] Authorization token captured from: https://api.underdogfantasy.com...
[TokenCapture] Device ID captured: ud-device-12345
```

### Screenshot Documentation

Debug screenshots are automatically saved:
- `login-before-attempt-*.png` - Pre-login state
- `email-typed-*.png` - Email entry verification
- `password-typed-*.png` - Password entry verification
- `login-form-filled-*.png` - Complete form state
- `token-capture-failed-*.png` - Token capture failures

### Network Monitoring

All API requests are logged:
```
[NetworkMonitor] Response: https://api.underdogfantasy.com/v1/auth - Status: 200
[NetworkMonitor] Response: https://login.underdogsports.com/oauth/token - Status: 200
```

## 🛠️ Troubleshooting

### Issue 1: Proxy Connection Failed
```
Error: Proxy test failed: connect ECONNREFUSED
```

**Solution:**
1. Verify proxy credentials in `.env`
2. Test proxy manually with curl
3. Check proxy server status
4. Ensure proxy supports HTTP CONNECT method

### Issue 2: User-Location-Token Not Captured
```
Failed to capture authentication tokens
encodedResponse: false
```

**Solution (Enhanced in v1.4):**
1. System now tries multiple capture methods:
   - Request headers (6+ formats)
   - Response body JSON parsing
   - JavaScript variables scanning
   - localStorage/sessionStorage
   - Cookie extraction

2. Extended timeout to 45 seconds
3. Progressive verification every 10 seconds
4. Multi-page navigation triggers

### Issue 3: Login Successful but API Fails
```
Login successful, collecting cookies...
Waiting for tokens... { encodedResponse: false }
```

**Solution:**
The v1.4 system includes:
- GeoComply function triggering
- Interactive element clicking
- Page refresh strategies
- Comprehensive variable scanning

## 📊 Performance Monitoring

### Key Metrics to Monitor

1. **Initialization Time**: Should complete within 60-90 seconds
2. **Token Capture Success**: All 3 tokens (auth, device, location) captured
3. **API Response Time**: Bet link generation under 10 seconds
4. **Memory Usage**: Browser should stay under 500MB

### Log Analysis

Monitor these log patterns:
```bash
# Successful initialization
grep "CRITICAL SUCCESS" logs

# Token capture status
grep "TokenCapture" logs

# API request monitoring
grep "NetworkMonitor" logs

# Error patterns
grep "CRITICAL FAILURE\|Failed to capture" logs
```

## 🔄 Maintenance

### Daily Tasks
- Monitor log files for errors
- Check proxy connectivity
- Verify token capture success rates

### Weekly Tasks
- Clear debug screenshots (*.png, *.html)
- Update proxy credentials if needed
- Review performance metrics

### Monthly Tasks
- Update dependencies: `npm update`
- Review and rotate credentials
- Performance optimization review

## 🚨 Emergency Recovery

If the service fails:

1. **Check proxy status**:
   ```bash
   curl --proxy **********************:port https://ipinfo.io
   ```

2. **Restart with clean state**:
   ```bash
   rm -rf user_data/
   node server.js
   ```

3. **Enable verbose debugging**:
   ```bash
   HEADLESS=false node server.js
   ```

4. **Check recent screenshots** for visual debugging

## 📞 Support Checklist

Before reporting issues:

- [ ] Proxy connectivity verified
- [ ] Credentials confirmed working
- [ ] Latest debug screenshots reviewed
- [ ] Health endpoint returning "ready"
- [ ] Log files checked for specific errors
- [ ] v1.4 changelog reviewed for known issues

## 🎯 Success Criteria

Deployment is successful when:

1. ✅ Health endpoint returns `"status": "ready"`
2. ✅ Proxy test shows US IP address
3. ✅ Login completes without errors
4. ✅ All 3 tokens captured (auth, device, location)
5. ✅ Test API call returns valid bet link
6. ✅ No critical errors in logs

The v1.4 enhancement specifically addresses User-Location-Token capture, which was the primary blocker for successful bet link generation.
